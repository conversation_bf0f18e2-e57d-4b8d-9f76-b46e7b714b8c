{"multipart": [{"apply": {"model": "biomesoplenty:block/toadstool_block"}, "when": {"north": "true"}}, {"apply": {"model": "biomesoplenty:block/toadstool_block", "uvlock": true, "y": 90}, "when": {"east": "true"}}, {"apply": {"model": "biomesoplenty:block/toadstool_block", "uvlock": true, "y": 180}, "when": {"south": "true"}}, {"apply": {"model": "biomesoplenty:block/toadstool_block", "uvlock": true, "y": 270}, "when": {"west": "true"}}, {"apply": {"model": "biomesoplenty:block/toadstool_block", "uvlock": true, "x": 270}, "when": {"up": "true"}}, {"apply": {"model": "biomesoplenty:block/toadstool_block", "uvlock": true, "x": 90}, "when": {"down": "true"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside"}, "when": {"north": "false"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside", "y": 90}, "when": {"east": "false"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside", "y": 180}, "when": {"south": "false"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside", "y": 270}, "when": {"west": "false"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside", "x": 270}, "when": {"up": "false"}}, {"apply": {"model": "minecraft:block/mushroom_block_inside", "x": 90}, "when": {"down": "false"}}]}